# 内网穿透功能使用指南

## 🚇 功能概述

LMArena代理服务器现在支持内网穿透功能，让您可以从任何地方访问您的本地服务器。支持多种内网穿透服务：

- **Ngrok** - 最流行的内网穿透服务
- **FRP** - 开源的内网穿透解决方案
- **Cloudflare Tunnel** - Cloudflare提供的免费隧道服务
- **LocalTunnel** - 简单易用的内网穿透工具

## 📋 配置方法

### 方法1: 通过监控面板配置（推荐）

1. 访问监控面板：`http://localhost:9081/monitor`
2. 点击"系统设置"标签
3. 在"内网穿透配置"部分进行设置
4. 点击"保存配置"
5. 使用API端点启动隧道

### 方法2: 通过配置文件

编辑 `logs/config.json` 文件中的 `tunnel` 部分：

```json
{
  "tunnel": {
    "enabled": true,
    "type": "ngrok",
    "auth_token": "your_ngrok_auth_token",
    "subdomain": "your_subdomain",
    "region": "us",
    "custom_domain": "",
    "frp_server": "",
    "frp_port": 7000,
    "frp_token": "",
    "cloudflare_tunnel_token": ""
  }
}
```

## 🔧 各种隧道服务配置

### Ngrok 配置

1. **注册账号**：访问 [ngrok.com](https://ngrok.com) 注册免费账号
2. **获取Auth Token**：在dashboard中获取您的auth token
3. **配置参数**：
   ```json
   {
     "enabled": true,
     "type": "ngrok",
     "auth_token": "your_ngrok_auth_token",
     "subdomain": "my-lmarena",  // 可选，需要付费账号
     "region": "us"  // us, eu, ap, au, sa, jp, in
   }
   ```

### FRP 配置

1. **部署FRP服务器**：在有公网IP的服务器上部署frps
2. **配置参数**：
   ```json
   {
     "enabled": true,
     "type": "frp",
     "frp_server": "your_server_ip",
     "frp_port": 7000,
     "frp_token": "your_token",
     "custom_domain": "lmarena.yourdomain.com"
   }
   ```

### Cloudflare Tunnel 配置

1. **安装cloudflared**：下载并安装cloudflared客户端
2. **创建隧道**：使用cloudflare dashboard创建隧道
3. **配置参数**：
   ```json
   {
     "enabled": true,
     "type": "cloudflare",
     "cloudflare_tunnel_token": "your_tunnel_token"
   }
   ```

### LocalTunnel 配置

1. **安装localtunnel**：`npm install -g localtunnel`
2. **配置参数**：
   ```json
   {
     "enabled": true,
     "type": "localtunnel",
     "subdomain": "my-lmarena"  // 可选
   }
   ```

## 🚀 启动内网穿透

### 通过API启动

```bash
# 启动隧道
curl -X POST http://localhost:9081/api/tunnel/start

# 查看状态
curl http://localhost:9081/api/tunnel/status

# 停止隧道
curl -X POST http://localhost:9081/api/tunnel/stop

# 重启隧道
curl -X POST http://localhost:9081/api/tunnel/restart
```

### 通过监控面板

1. 访问监控面板
2. 在"系统信息"部分查看隧道状态
3. 使用控制按钮启动/停止隧道

## 📊 监控和状态

### 状态查询

访问 `/api/tunnel/status` 获取详细状态：

```json
{
  "enabled": true,
  "type": "ngrok",
  "status": "running",
  "public_url": "https://abc123.ngrok.io",
  "error_message": "",
  "local_port": 9081
}
```

### 系统信息

访问 `/api/system/info` 查看包含公网URL的系统信息：

```json
{
  "server_urls": {
    "local": "http://localhost:9081",
    "network": "http://*************:9081",
    "public": "https://abc123.ngrok.io",
    "public_monitor": "https://abc123.ngrok.io/monitor"
  }
}
```

## 🔒 安全注意事项

1. **保护Auth Token**：不要在公开场所分享您的认证令牌
2. **使用HTTPS**：生产环境建议使用HTTPS隧道
3. **访问控制**：考虑在隧道前添加身份验证
4. **监控访问**：定期检查访问日志

## 🐛 故障排除

### 常见问题

1. **隧道启动失败**
   - 检查网络连接
   - 验证认证令牌
   - 查看错误日志

2. **无法访问公网URL**
   - 确认隧道状态为"running"
   - 检查防火墙设置
   - 验证本地服务是否正常

3. **连接不稳定**
   - 检查网络质量
   - 尝试更换隧道服务
   - 查看系统资源使用情况

### 日志查看

```bash
# 查看服务器日志
tail -f logs/server.log

# 查看错误日志
tail -f logs/errors.jsonl
```

## 💡 最佳实践

1. **选择合适的服务**：
   - 开发测试：Ngrok或LocalTunnel
   - 生产环境：FRP或Cloudflare Tunnel

2. **性能优化**：
   - 选择就近的服务器区域
   - 监控延迟和带宽使用

3. **备用方案**：
   - 配置多个隧道服务
   - 准备故障切换方案

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件
2. 检查网络连接
3. 验证配置参数
4. 提交Issue到GitHub仓库

---

**注意**：内网穿透会将您的本地服务暴露到公网，请确保采取适当的安全措施。
