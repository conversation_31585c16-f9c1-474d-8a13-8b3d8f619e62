#!/usr/bin/env python3
"""
内网穿透功能测试脚本
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:9081"

def test_tunnel_api():
    """测试内网穿透API"""
    print("🧪 测试内网穿透API功能...")
    
    # 1. 获取当前状态
    print("\n1. 获取隧道状态...")
    try:
        response = requests.get(f"{BASE_URL}/api/tunnel/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ 状态获取成功: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 状态获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 2. 获取系统信息
    print("\n2. 获取系统信息...")
    try:
        response = requests.get(f"{BASE_URL}/api/system/info")
        if response.status_code == 200:
            info = response.json()
            print(f"   ✅ 系统信息获取成功:")
            print(f"      本地URL: {info['server_urls']['local']}")
            print(f"      网络URL: {info['server_urls']['network']}")
            if 'public' in info['server_urls']:
                print(f"      公网URL: {info['server_urls']['public']}")
            else:
                print("      公网URL: 未启用")
        else:
            print(f"   ❌ 系统信息获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def test_tunnel_config():
    """测试隧道配置"""
    print("\n🔧 测试隧道配置...")
    
    # 获取当前配置
    try:
        response = requests.get(f"{BASE_URL}/api/config")
        if response.status_code == 200:
            config = response.json()
            tunnel_config = config.get('tunnel', {})
            print(f"   当前隧道配置: {json.dumps(tunnel_config, indent=2, ensure_ascii=False)}")
            
            # 如果隧道未启用，提供配置示例
            if not tunnel_config.get('enabled', False):
                print("\n   💡 隧道未启用，以下是配置示例:")
                example_config = {
                    "tunnel": {
                        "enabled": True,
                        "type": "ngrok",
                        "auth_token": "your_ngrok_auth_token_here",
                        "subdomain": "",
                        "region": "us"
                    }
                }
                print(f"   {json.dumps(example_config, indent=2, ensure_ascii=False)}")
                print("\n   📝 要启用隧道，请:")
                print("   1. 访问监控面板: http://localhost:9081/monitor")
                print("   2. 在系统设置中配置隧道参数")
                print("   3. 或使用API更新配置")
        else:
            print(f"   ❌ 配置获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def test_tunnel_control():
    """测试隧道控制功能"""
    print("\n🎮 测试隧道控制功能...")
    
    # 检查当前状态
    try:
        response = requests.get(f"{BASE_URL}/api/tunnel/status")
        if response.status_code == 200:
            status = response.json()
            if status.get('enabled', False):
                print("   隧道已启用，测试控制功能...")
                
                # 测试启动
                print("\n   测试启动隧道...")
                start_response = requests.post(f"{BASE_URL}/api/tunnel/start")
                if start_response.status_code == 200:
                    result = start_response.json()
                    print(f"   ✅ 启动成功: {result.get('message', '')}")
                    if 'public_url' in result:
                        print(f"   🌐 公网URL: {result['public_url']}")
                else:
                    print(f"   ⚠️  启动响应: {start_response.status_code} - {start_response.text}")
                
                # 等待一下
                time.sleep(2)
                
                # 再次检查状态
                status_response = requests.get(f"{BASE_URL}/api/tunnel/status")
                if status_response.status_code == 200:
                    new_status = status_response.json()
                    print(f"   📊 当前状态: {new_status.get('status', 'unknown')}")
                    if new_status.get('public_url'):
                        print(f"   🔗 公网地址: {new_status['public_url']}")
                
            else:
                print("   ⚠️  隧道未启用，跳过控制测试")
        else:
            print(f"   ❌ 状态检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 控制测试失败: {e}")

def main():
    """主函数"""
    print("🚇 LMArena 内网穿透功能测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print(f"   请确保服务器在 {BASE_URL} 上运行")
        return
    
    # 运行测试
    test_tunnel_api()
    test_tunnel_config()
    test_tunnel_control()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n📖 更多信息请查看:")
    print("   - 监控面板: http://localhost:9081/monitor")
    print("   - 配置指南: docs/tunnel_guide.md")
    print("   - API文档: http://localhost:9081/monitor#api-docs")

if __name__ == "__main__":
    main()
