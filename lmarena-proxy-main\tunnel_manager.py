"""
内网穿透管理器
支持多种内网穿透服务：ngrok, frp, cloudflare tunnel等
"""

import asyncio
import logging
import subprocess
import json
import time
import os
import requests
from typing import Optional, Dict, Any
from pathlib import Path
from dataclasses import dataclass
from enum import Enum


class TunnelType(Enum):
    """内网穿透类型"""
    NGROK = "ngrok"
    FRP = "frp"
    CLOUDFLARE = "cloudflare"
    LOCALTUNNEL = "localtunnel"


@dataclass
class TunnelConfig:
    """内网穿透配置"""
    enabled: bool = False
    tunnel_type: TunnelType = TunnelType.NGROK
    local_port: int = 9080
    auth_token: str = ""
    subdomain: str = ""
    region: str = "us"
    custom_domain: str = ""
    frp_server: str = ""
    frp_port: int = 7000
    frp_token: str = ""
    cloudflare_tunnel_token: str = ""


class TunnelManager:
    """内网穿透管理器"""
    
    def __init__(self, config: TunnelConfig):
        self.config = config
        self.process: Optional[subprocess.Popen] = None
        self.public_url: Optional[str] = None
        self.status = "stopped"
        self.error_message = ""
        
    async def start_tunnel(self) -> bool:
        """启动内网穿透"""
        if not self.config.enabled:
            logging.info("内网穿透未启用")
            return False
            
        try:
            if self.config.tunnel_type == TunnelType.NGROK:
                return await self._start_ngrok()
            elif self.config.tunnel_type == TunnelType.FRP:
                return await self._start_frp()
            elif self.config.tunnel_type == TunnelType.CLOUDFLARE:
                return await self._start_cloudflare()
            elif self.config.tunnel_type == TunnelType.LOCALTUNNEL:
                return await self._start_localtunnel()
            else:
                self.error_message = f"不支持的内网穿透类型: {self.config.tunnel_type}"
                logging.error(self.error_message)
                return False
                
        except Exception as e:
            self.error_message = f"启动内网穿透失败: {str(e)}"
            logging.error(self.error_message)
            self.status = "error"
            return False
    
    async def _start_ngrok(self) -> bool:
        """启动ngrok"""
        try:
            # 检查ngrok是否安装
            result = subprocess.run(["ngrok", "version"], capture_output=True, text=True)
            if result.returncode != 0:
                # 尝试使用pyngrok
                from pyngrok import ngrok
                
                # 设置auth token
                if self.config.auth_token:
                    ngrok.set_auth_token(self.config.auth_token)
                
                # 启动隧道
                tunnel_options = {
                    "addr": self.config.local_port,
                    "proto": "http"
                }
                
                if self.config.subdomain:
                    tunnel_options["subdomain"] = self.config.subdomain
                if self.config.region:
                    tunnel_options["region"] = self.config.region
                
                tunnel = ngrok.connect(**tunnel_options)
                self.public_url = tunnel.public_url
                self.status = "running"
                
                logging.info(f"✅ Ngrok隧道已启动: {self.public_url}")
                return True
                
            else:
                # 使用命令行ngrok
                cmd = ["ngrok", "http", str(self.config.local_port)]
                
                if self.config.auth_token:
                    # 设置auth token
                    subprocess.run(["ngrok", "authtoken", self.config.auth_token])
                
                if self.config.subdomain:
                    cmd.extend(["--subdomain", self.config.subdomain])
                if self.config.region:
                    cmd.extend(["--region", self.config.region])
                
                self.process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                # 等待ngrok启动并获取公网URL
                await asyncio.sleep(3)
                self.public_url = await self._get_ngrok_url()
                
                if self.public_url:
                    self.status = "running"
                    logging.info(f"✅ Ngrok隧道已启动: {self.public_url}")
                    return True
                else:
                    self.error_message = "无法获取ngrok公网URL"
                    return False
                    
        except ImportError:
            self.error_message = "请安装pyngrok: pip install pyngrok"
            logging.error(self.error_message)
            return False
        except Exception as e:
            self.error_message = f"启动ngrok失败: {str(e)}"
            logging.error(self.error_message)
            return False
    
    async def _get_ngrok_url(self) -> Optional[str]:
        """获取ngrok的公网URL"""
        try:
            response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get("tunnels", [])
                for tunnel in tunnels:
                    if tunnel.get("proto") == "https":
                        return tunnel.get("public_url")
                    elif tunnel.get("proto") == "http":
                        return tunnel.get("public_url")
            return None
        except Exception as e:
            logging.error(f"获取ngrok URL失败: {e}")
            return None
    
    async def _start_frp(self) -> bool:
        """启动frp客户端"""
        try:
            # 创建frp配置文件
            frp_config = f"""[common]
server_addr = {self.config.frp_server}
server_port = {self.config.frp_port}
token = {self.config.frp_token}

[lmarena-proxy]
type = http
local_ip = 127.0.0.1
local_port = {self.config.local_port}
custom_domains = {self.config.custom_domain or 'lmarena.example.com'}
"""
            
            config_path = Path("frpc.ini")
            with open(config_path, "w", encoding="utf-8") as f:
                f.write(frp_config)
            
            # 启动frp客户端
            cmd = ["frpc", "-c", str(config_path)]
            self.process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            await asyncio.sleep(2)
            
            if self.process.poll() is None:  # 进程仍在运行
                self.public_url = f"http://{self.config.custom_domain or 'lmarena.example.com'}"
                self.status = "running"
                logging.info(f"✅ FRP隧道已启动: {self.public_url}")
                return True
            else:
                self.error_message = "FRP客户端启动失败"
                return False
                
        except Exception as e:
            self.error_message = f"启动FRP失败: {str(e)}"
            logging.error(self.error_message)
            return False
    
    async def _start_cloudflare(self) -> bool:
        """启动Cloudflare Tunnel"""
        try:
            if not self.config.cloudflare_tunnel_token:
                self.error_message = "需要Cloudflare Tunnel Token"
                return False
            
            cmd = [
                "cloudflared", "tunnel", "run",
                "--token", self.config.cloudflare_tunnel_token
            ]
            
            self.process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            await asyncio.sleep(3)
            
            if self.process.poll() is None:
                self.status = "running"
                self.public_url = "https://your-tunnel.trycloudflare.com"  # 需要从cloudflared获取实际URL
                logging.info(f"✅ Cloudflare隧道已启动")
                return True
            else:
                self.error_message = "Cloudflare隧道启动失败"
                return False
                
        except Exception as e:
            self.error_message = f"启动Cloudflare隧道失败: {str(e)}"
            logging.error(self.error_message)
            return False
    
    async def _start_localtunnel(self) -> bool:
        """启动localtunnel"""
        try:
            cmd = ["lt", "--port", str(self.config.local_port)]
            
            if self.config.subdomain:
                cmd.extend(["--subdomain", self.config.subdomain])
            
            self.process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 读取输出获取URL
            await asyncio.sleep(3)
            
            if self.process.poll() is None:
                # 尝试从输出中解析URL
                try:
                    stdout, _ = self.process.communicate(timeout=1)
                    for line in stdout.split('\n'):
                        if 'https://' in line and 'localtunnel.me' in line:
                            self.public_url = line.strip()
                            break
                except subprocess.TimeoutExpired:
                    pass
                
                if not self.public_url:
                    self.public_url = f"https://{self.config.subdomain or 'random'}.localtunnel.me"
                
                self.status = "running"
                logging.info(f"✅ LocalTunnel隧道已启动: {self.public_url}")
                return True
            else:
                self.error_message = "LocalTunnel启动失败"
                return False
                
        except Exception as e:
            self.error_message = f"启动LocalTunnel失败: {str(e)}"
            logging.error(self.error_message)
            return False
    
    async def stop_tunnel(self):
        """停止内网穿透"""
        try:
            if self.config.tunnel_type == TunnelType.NGROK:
                try:
                    from pyngrok import ngrok
                    ngrok.kill()
                except ImportError:
                    pass
                except Exception as e:
                    logging.error(f"停止ngrok失败: {e}")
            
            if self.process:
                self.process.terminate()
                try:
                    await asyncio.wait_for(asyncio.create_task(self._wait_for_process()), timeout=5)
                except asyncio.TimeoutError:
                    self.process.kill()
                
                self.process = None
            
            self.status = "stopped"
            self.public_url = None
            logging.info("内网穿透已停止")
            
        except Exception as e:
            logging.error(f"停止内网穿透失败: {e}")
    
    async def _wait_for_process(self):
        """等待进程结束"""
        while self.process and self.process.poll() is None:
            await asyncio.sleep(0.1)
    
    def get_status(self) -> Dict[str, Any]:
        """获取隧道状态"""
        return {
            "enabled": self.config.enabled,
            "type": self.config.tunnel_type.value,
            "status": self.status,
            "public_url": self.public_url,
            "error_message": self.error_message,
            "local_port": self.config.local_port
        }
